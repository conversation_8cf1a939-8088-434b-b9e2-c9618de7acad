{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 19816, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 19816, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 19816, "tid": 12, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 19816, "tid": 12, "ts": 1755896961518532, "dur": 764, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 19816, "tid": 12, "ts": 1755896961522681, "dur": 702, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 19816, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 19816, "tid": 1, "ts": 1755896961004264, "dur": 22097, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 19816, "tid": 1, "ts": 1755896961026365, "dur": 57888, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 19816, "tid": 1, "ts": 1755896961084264, "dur": 49456, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 19816, "tid": 12, "ts": 1755896961523386, "dur": 989, "ph": "X", "name": "", "args": {}}, {"pid": 19816, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961002601, "dur": 4909, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961007512, "dur": 502466, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961008321, "dur": 1978, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961010303, "dur": 474, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961010779, "dur": 3984, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961014771, "dur": 178, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961014951, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961015006, "dur": 447, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961015455, "dur": 7965, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961023423, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961023425, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961023483, "dur": 390, "ph": "X", "name": "ProcessMessages 542", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961023875, "dur": 118, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961023994, "dur": 4, "ph": "X", "name": "ProcessMessages 7104", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961023999, "dur": 35, "ph": "X", "name": "ReadAsync 7104", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961024037, "dur": 33, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961024072, "dur": 2, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961024075, "dur": 33, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961024110, "dur": 1, "ph": "X", "name": "ProcessMessages 846", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961024112, "dur": 19, "ph": "X", "name": "ReadAsync 846", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961024133, "dur": 42, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961024177, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961024201, "dur": 20, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961024224, "dur": 22, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961024248, "dur": 20, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961024270, "dur": 20, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961024293, "dur": 19, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961024314, "dur": 19, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961024335, "dur": 20, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961024357, "dur": 20, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961024379, "dur": 18, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961024399, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961024401, "dur": 20, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961024423, "dur": 19, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961024445, "dur": 19, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961024465, "dur": 53, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961024525, "dur": 3, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961024532, "dur": 64, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961024599, "dur": 1, "ph": "X", "name": "ProcessMessages 1284", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961024602, "dur": 44, "ph": "X", "name": "ReadAsync 1284", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961024649, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961024651, "dur": 36, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961024690, "dur": 1, "ph": "X", "name": "ProcessMessages 708", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961024692, "dur": 29, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961024724, "dur": 20, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961024748, "dur": 20, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961024770, "dur": 1, "ph": "X", "name": "ProcessMessages 575", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961024772, "dur": 22, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961024796, "dur": 64, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961024863, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961024884, "dur": 20, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961024907, "dur": 18, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961024928, "dur": 20, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961024949, "dur": 17, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961024969, "dur": 22, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961024994, "dur": 21, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961025017, "dur": 21, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961025040, "dur": 19, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961025061, "dur": 20, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961025083, "dur": 20, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961025106, "dur": 17, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961025125, "dur": 18, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961025145, "dur": 29, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961025175, "dur": 25, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961025203, "dur": 27, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961025233, "dur": 19, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961025254, "dur": 31, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961025289, "dur": 23, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961025314, "dur": 21, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961025337, "dur": 24, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961025364, "dur": 19, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961025384, "dur": 22, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961025408, "dur": 19, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961025430, "dur": 18, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961025449, "dur": 22, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961025474, "dur": 18, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961025494, "dur": 23, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961025518, "dur": 1, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961025520, "dur": 22, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961025544, "dur": 21, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961025568, "dur": 20, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961025590, "dur": 19, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961025611, "dur": 19, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961025632, "dur": 19, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961025653, "dur": 19, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961025674, "dur": 18, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961025695, "dur": 19, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961025716, "dur": 19, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961025737, "dur": 20, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961025759, "dur": 19, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961025779, "dur": 1, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961025781, "dur": 20, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961025803, "dur": 20, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961025825, "dur": 19, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961025849, "dur": 20, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961025871, "dur": 26, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961025899, "dur": 16, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961025915, "dur": 1, "ph": "X", "name": "ProcessMessages 190", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961025917, "dur": 19, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961025938, "dur": 19, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961025959, "dur": 35, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961025997, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961026029, "dur": 27, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961026058, "dur": 19, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961026080, "dur": 16, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961026097, "dur": 19, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961026118, "dur": 32, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961026153, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961026176, "dur": 20, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961026197, "dur": 21, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961026220, "dur": 21, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961026244, "dur": 20, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961026266, "dur": 20, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961026288, "dur": 18, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961026309, "dur": 64, "ph": "X", "name": "ReadAsync 166", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961026374, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961026395, "dur": 21, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961026418, "dur": 19, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961026439, "dur": 19, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961026460, "dur": 19, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961026481, "dur": 18, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961026500, "dur": 19, "ph": "X", "name": "ReadAsync 218", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961026521, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961026543, "dur": 19, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961026564, "dur": 21, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961026587, "dur": 21, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961026609, "dur": 18, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961026630, "dur": 16, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961026648, "dur": 19, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961026669, "dur": 21, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961026693, "dur": 29, "ph": "X", "name": "ReadAsync 152", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961026723, "dur": 20, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961026745, "dur": 26, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961026774, "dur": 18, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961026794, "dur": 19, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961026815, "dur": 18, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961026835, "dur": 18, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961026854, "dur": 1, "ph": "X", "name": "ProcessMessages 129", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961026855, "dur": 19, "ph": "X", "name": "ReadAsync 129", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961026876, "dur": 18, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961026896, "dur": 19, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961026918, "dur": 19, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961026939, "dur": 9, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961026950, "dur": 21, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961026973, "dur": 22, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961026999, "dur": 34, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961027033, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961027035, "dur": 22, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961027059, "dur": 19, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961027080, "dur": 25, "ph": "X", "name": "ReadAsync 211", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961027108, "dur": 19, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961027129, "dur": 457, "ph": "X", "name": "ReadAsync 54", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961027588, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961027614, "dur": 22, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961027638, "dur": 41, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961027681, "dur": 1, "ph": "X", "name": "ProcessMessages 444", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961027682, "dur": 21, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961027706, "dur": 20, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961027728, "dur": 19, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961027749, "dur": 19, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961027770, "dur": 19, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961027791, "dur": 19, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961027812, "dur": 10, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961027824, "dur": 25, "ph": "X", "name": "ReadAsync 111", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961027851, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961027854, "dur": 50, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961027906, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961027908, "dur": 28, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961027937, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961027939, "dur": 27, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961027969, "dur": 26, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961027997, "dur": 1, "ph": "X", "name": "ProcessMessages 532", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961027999, "dur": 17, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961028018, "dur": 19, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961028039, "dur": 19, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961028059, "dur": 21, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961028083, "dur": 17, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961028102, "dur": 19, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961028123, "dur": 19, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961028144, "dur": 18, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961028165, "dur": 20, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961028187, "dur": 19, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961028208, "dur": 20, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961028230, "dur": 25, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961028258, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961028286, "dur": 39, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961028327, "dur": 21, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961028348, "dur": 1, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961028350, "dur": 29, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961028381, "dur": 23, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961028406, "dur": 1, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961028408, "dur": 23, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961028433, "dur": 31, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961028467, "dur": 19, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961028488, "dur": 19, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961028508, "dur": 19, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961028529, "dur": 19, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961028550, "dur": 17, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961028569, "dur": 17, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961028588, "dur": 18, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961028608, "dur": 20, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961028631, "dur": 19, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961028652, "dur": 19, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961028674, "dur": 18, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961028694, "dur": 19, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961028714, "dur": 19, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961028735, "dur": 19, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961028756, "dur": 18, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961028776, "dur": 21, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961028799, "dur": 19, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961028820, "dur": 20, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961028842, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961028875, "dur": 19, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961028896, "dur": 1, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961028898, "dur": 32, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961028932, "dur": 17, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961028951, "dur": 20, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961028974, "dur": 28, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961029003, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961029030, "dur": 24, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961029056, "dur": 20, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961029078, "dur": 21, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961029101, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961029103, "dur": 27, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961029132, "dur": 20, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961029154, "dur": 21, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961029177, "dur": 29, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961029208, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961029237, "dur": 19, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961029258, "dur": 20, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961029280, "dur": 19, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961029302, "dur": 19, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961029323, "dur": 18, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961029342, "dur": 112, "ph": "X", "name": "ReadAsync 194", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961029456, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961029480, "dur": 21, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961029504, "dur": 19, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961029525, "dur": 20, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961029546, "dur": 19, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961029567, "dur": 17, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961029586, "dur": 20, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961029608, "dur": 17, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961029627, "dur": 20, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961029649, "dur": 19, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961029670, "dur": 20, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961029692, "dur": 22, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961029716, "dur": 26, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961029744, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961029765, "dur": 20, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961029787, "dur": 19, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961029808, "dur": 51, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961029862, "dur": 28, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961029893, "dur": 24, "ph": "X", "name": "ReadAsync 728", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961029920, "dur": 31, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961029953, "dur": 31, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961029986, "dur": 21, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961030008, "dur": 2, "ph": "X", "name": "ProcessMessages 391", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961030011, "dur": 19, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961030033, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961030064, "dur": 27, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961030093, "dur": 25, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961030120, "dur": 30, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961030154, "dur": 22, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961030179, "dur": 28, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961030208, "dur": 27, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961030238, "dur": 27, "ph": "X", "name": "ReadAsync 744", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961033315, "dur": 1, "ph": "X", "name": "ProcessMessages 559", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961033316, "dur": 160, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961033478, "dur": 12, "ph": "X", "name": "ProcessMessages 20481", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961033491, "dur": 21, "ph": "X", "name": "ReadAsync 20481", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961033515, "dur": 24, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961033542, "dur": 23, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961033567, "dur": 34, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961033603, "dur": 32, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961033638, "dur": 23, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961033663, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961033688, "dur": 23, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961033714, "dur": 24, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961033740, "dur": 22, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961033765, "dur": 23, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961033790, "dur": 32, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961033824, "dur": 32, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961033859, "dur": 24, "ph": "X", "name": "ReadAsync 186", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961033885, "dur": 22, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961033909, "dur": 106, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961034017, "dur": 1, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961034019, "dur": 45, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961034065, "dur": 1, "ph": "X", "name": "ProcessMessages 1636", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961034068, "dur": 53, "ph": "X", "name": "ReadAsync 1636", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961034123, "dur": 1, "ph": "X", "name": "ProcessMessages 88", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961034124, "dur": 36, "ph": "X", "name": "ReadAsync 88", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961034185, "dur": 1, "ph": "X", "name": "ProcessMessages 1249", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961034187, "dur": 59, "ph": "X", "name": "ReadAsync 1249", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961034247, "dur": 1, "ph": "X", "name": "ProcessMessages 1164", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961034249, "dur": 22, "ph": "X", "name": "ReadAsync 1164", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961034298, "dur": 28, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961034327, "dur": 1, "ph": "X", "name": "ProcessMessages 788", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961034329, "dur": 50, "ph": "X", "name": "ReadAsync 788", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961034381, "dur": 583, "ph": "X", "name": "ReadAsync 150", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961034967, "dur": 79, "ph": "X", "name": "ReadAsync 768", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961035047, "dur": 5, "ph": "X", "name": "ProcessMessages 10112", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961035053, "dur": 30, "ph": "X", "name": "ReadAsync 10112", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961035086, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961035088, "dur": 34, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961035124, "dur": 1, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961035126, "dur": 20, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961035178, "dur": 24, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961035203, "dur": 1, "ph": "X", "name": "ProcessMessages 1039", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961035204, "dur": 22, "ph": "X", "name": "ReadAsync 1039", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961035228, "dur": 20, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961035251, "dur": 22, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961035275, "dur": 18, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961035294, "dur": 26, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961035322, "dur": 21, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961035345, "dur": 19, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961035366, "dur": 18, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961035386, "dur": 18, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961035406, "dur": 18, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961035426, "dur": 19, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961035447, "dur": 17, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961035466, "dur": 17, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961035485, "dur": 17, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961035505, "dur": 19, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961035526, "dur": 44, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961035574, "dur": 29, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961035606, "dur": 20, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961035628, "dur": 19, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961035650, "dur": 17, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961035669, "dur": 19, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961035690, "dur": 19, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961035711, "dur": 19, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961035732, "dur": 28, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961035762, "dur": 22, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961035786, "dur": 54, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961035843, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961035876, "dur": 22, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961035900, "dur": 20, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961035922, "dur": 22, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961035947, "dur": 20, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961035969, "dur": 17, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961035989, "dur": 26, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961036017, "dur": 22, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961036041, "dur": 19, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961036062, "dur": 20, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961036084, "dur": 18, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961036105, "dur": 17, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961036123, "dur": 21, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961036146, "dur": 19, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961036167, "dur": 17, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961036186, "dur": 19, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961036207, "dur": 19, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961036228, "dur": 17, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961036247, "dur": 18, "ph": "X", "name": "ReadAsync 159", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961036267, "dur": 18, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961036287, "dur": 18, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961036309, "dur": 19, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961036330, "dur": 20, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961036352, "dur": 18, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961036372, "dur": 18, "ph": "X", "name": "ReadAsync 103", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961036393, "dur": 19, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961036414, "dur": 20, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961036436, "dur": 20, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961036459, "dur": 22, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961036483, "dur": 19, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961036504, "dur": 17, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961036523, "dur": 21, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961036546, "dur": 19, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961036567, "dur": 19, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961036589, "dur": 18, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961036610, "dur": 18, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961036630, "dur": 17, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961036649, "dur": 19, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961036670, "dur": 25, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961036696, "dur": 20, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961036719, "dur": 22, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961036743, "dur": 19, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961036764, "dur": 19, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961036785, "dur": 20, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961036807, "dur": 19, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961036828, "dur": 26, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961036856, "dur": 21, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961036879, "dur": 19, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961036900, "dur": 40, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961036942, "dur": 1, "ph": "X", "name": "ProcessMessages 501", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961036943, "dur": 27, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961036974, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961037021, "dur": 1, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961037023, "dur": 32, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961037055, "dur": 1, "ph": "X", "name": "ProcessMessages 834", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961037058, "dur": 24, "ph": "X", "name": "ReadAsync 834", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961037085, "dur": 29, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961037117, "dur": 30, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961037148, "dur": 22, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961037172, "dur": 45, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961037220, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961037247, "dur": 23, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961037272, "dur": 19, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961037293, "dur": 76, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961037371, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961037402, "dur": 20, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961037425, "dur": 19, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961037446, "dur": 69, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961037517, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961037536, "dur": 21, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961037559, "dur": 20, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961037581, "dur": 17, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961037601, "dur": 64, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961037666, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961037689, "dur": 20, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961037711, "dur": 20, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961037733, "dur": 66, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961037801, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961037823, "dur": 18, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961037843, "dur": 18, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961037864, "dur": 72, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961037938, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961037959, "dur": 21, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961037982, "dur": 18, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961038003, "dur": 17, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961038021, "dur": 61, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961038084, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961038106, "dur": 19, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961038127, "dur": 19, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961038148, "dur": 72, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961038222, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961038244, "dur": 20, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961038265, "dur": 19, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961038286, "dur": 19, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961038307, "dur": 68, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961038377, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961038397, "dur": 19, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961038418, "dur": 20, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961038440, "dur": 71, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961038512, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961038534, "dur": 21, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961038558, "dur": 18, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961038578, "dur": 17, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961038597, "dur": 62, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961038660, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961038682, "dur": 19, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961038703, "dur": 19, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961038724, "dur": 77, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961038803, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961038825, "dur": 19, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961038846, "dur": 19, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961038867, "dur": 72, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961038941, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961038962, "dur": 19, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961038983, "dur": 19, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961039004, "dur": 70, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961039076, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961039097, "dur": 18, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961039117, "dur": 20, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961039139, "dur": 17, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961039158, "dur": 63, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961039223, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961039244, "dur": 20, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961039265, "dur": 20, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961039287, "dur": 71, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961039360, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961039380, "dur": 18, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961039401, "dur": 19, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961039422, "dur": 71, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961039495, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961039517, "dur": 19, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961039538, "dur": 19, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961039560, "dur": 70, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961039631, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961039654, "dur": 18, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961039673, "dur": 20, "ph": "X", "name": "ReadAsync 117", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961039695, "dur": 17, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961039715, "dur": 60, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961039776, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961039798, "dur": 34, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961039834, "dur": 21, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961039857, "dur": 27, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961039886, "dur": 23, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961039911, "dur": 72, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961039986, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961040017, "dur": 24, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961040043, "dur": 57, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961040104, "dur": 1, "ph": "X", "name": "ProcessMessages 401", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961040106, "dur": 49, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961040158, "dur": 2, "ph": "X", "name": "ProcessMessages 1119", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961040161, "dur": 43, "ph": "X", "name": "ReadAsync 1119", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961040206, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961040207, "dur": 31, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961040241, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961040243, "dur": 63, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961040308, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961040340, "dur": 21, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961040364, "dur": 20, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961040387, "dur": 73, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961040463, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961040492, "dur": 24, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961040518, "dur": 24, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961040544, "dur": 23, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961040569, "dur": 21, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961040592, "dur": 21, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961040615, "dur": 20, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961040637, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961040661, "dur": 19, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961040682, "dur": 72, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961040757, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961040797, "dur": 1, "ph": "X", "name": "ProcessMessages 722", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961040799, "dur": 30, "ph": "X", "name": "ReadAsync 722", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961040830, "dur": 1, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961040831, "dur": 75, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961040910, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961040912, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961040954, "dur": 1, "ph": "X", "name": "ProcessMessages 874", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961040956, "dur": 34, "ph": "X", "name": "ReadAsync 874", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961040993, "dur": 40, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961041035, "dur": 1, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961041037, "dur": 35, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961041075, "dur": 1, "ph": "X", "name": "ProcessMessages 486", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961041077, "dur": 29, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961041108, "dur": 31, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961041142, "dur": 25, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961041169, "dur": 74, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961041246, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961041285, "dur": 1, "ph": "X", "name": "ProcessMessages 537", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961041287, "dur": 36, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961041325, "dur": 26, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961041355, "dur": 57, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961041415, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961041453, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961041455, "dur": 34, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961041491, "dur": 25, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961041519, "dur": 59, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961041581, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961041611, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961041613, "dur": 27, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961041642, "dur": 27, "ph": "X", "name": "ReadAsync 746", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961041672, "dur": 26, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961041700, "dur": 26, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961041728, "dur": 24, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961041754, "dur": 28, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961041784, "dur": 52, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961041840, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961041875, "dur": 1, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961041876, "dur": 29, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961041908, "dur": 30, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961041940, "dur": 29, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961041972, "dur": 37, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961042011, "dur": 27, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961042041, "dur": 26, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961042069, "dur": 72, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961042144, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961042177, "dur": 29, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961042208, "dur": 29, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961042241, "dur": 22, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961042266, "dur": 61, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961042329, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961042365, "dur": 47, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961042414, "dur": 27, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961042443, "dur": 1, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961042444, "dur": 65, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961042512, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961042547, "dur": 41, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961042591, "dur": 25, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961042619, "dur": 58, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961042679, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961042712, "dur": 35, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961042750, "dur": 29, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961042782, "dur": 25, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961042809, "dur": 70, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961042881, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961042913, "dur": 33, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961042949, "dur": 27, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961042978, "dur": 68, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961043050, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961043083, "dur": 35, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961043120, "dur": 67, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961043189, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961043253, "dur": 1, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961043254, "dur": 26, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961043284, "dur": 69, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961043356, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961043389, "dur": 26, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961043417, "dur": 24, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961043444, "dur": 27, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961043473, "dur": 62, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961043538, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961043575, "dur": 27, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961043604, "dur": 25, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961043632, "dur": 63, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961043698, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961043734, "dur": 32, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961043767, "dur": 23, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961043792, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961043794, "dur": 61, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961043856, "dur": 235, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961044094, "dur": 24, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961044121, "dur": 25, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961044149, "dur": 77, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961044232, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961044234, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961044264, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961044266, "dur": 32, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961044301, "dur": 21, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961044323, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961044325, "dur": 78, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961044407, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961044444, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961044446, "dur": 41, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961044489, "dur": 1, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961044491, "dur": 82, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961044577, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961044633, "dur": 1, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961044635, "dur": 38, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961044675, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961044678, "dur": 35, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961044716, "dur": 1, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961044718, "dur": 2521, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961047249, "dur": 317, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961047569, "dur": 309, "ph": "X", "name": "ProcessMessages 20210", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961047881, "dur": 67, "ph": "X", "name": "ReadAsync 20210", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961047951, "dur": 5, "ph": "X", "name": "ProcessMessages 576", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961047957, "dur": 37, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961047996, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961048000, "dur": 34, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961048036, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961048038, "dur": 37, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961048079, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961048081, "dur": 40, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961048125, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961048128, "dur": 39, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961048170, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961048173, "dur": 60, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961048236, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961048238, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961048285, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961048288, "dur": 32, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961048323, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961048325, "dur": 40, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961048367, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961048370, "dur": 35, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961048407, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961048410, "dur": 43, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961048455, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961048458, "dur": 34, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961048497, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961048499, "dur": 45, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961048547, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961048552, "dur": 1571, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961050127, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961050130, "dur": 158, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961050292, "dur": 18, "ph": "X", "name": "ProcessMessages 4652", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961050311, "dur": 45, "ph": "X", "name": "ReadAsync 4652", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961050360, "dur": 2, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961050364, "dur": 47, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961050415, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961050417, "dur": 41, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961050461, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961050463, "dur": 43, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961050509, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961050512, "dur": 29, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961050546, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961050578, "dur": 47, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961050628, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961050630, "dur": 14136, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961064772, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961064775, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961064814, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961064817, "dur": 702, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961065522, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961065551, "dur": 94, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961065650, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961065652, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961065694, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961065696, "dur": 122, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961065821, "dur": 1007, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961066834, "dur": 2, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961066838, "dur": 37, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961066879, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961066883, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961066930, "dur": 5, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961066938, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961066980, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961067020, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961067022, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961067061, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961067063, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961067105, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961067109, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961067144, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961067147, "dur": 68, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961067218, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961067220, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961067265, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961067269, "dur": 312, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961067585, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961067588, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961067627, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961067631, "dur": 76, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961067712, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961067757, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961067759, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961067797, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961067799, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961067834, "dur": 59, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961067896, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961067899, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961067940, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961067979, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961068014, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961068044, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961068045, "dur": 35, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961068084, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961068116, "dur": 32, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961068151, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961068188, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961068191, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961068237, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961068240, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961068288, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961068290, "dur": 37, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961068329, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961068332, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961068368, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961068370, "dur": 37, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961068410, "dur": 32, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961068445, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961068448, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961068515, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961068549, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961068553, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961068585, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961068616, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961068618, "dur": 80, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961068704, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961068706, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961068755, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961068757, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961068787, "dur": 183, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961068973, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961068975, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961069007, "dur": 99, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961069110, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961069149, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961069187, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961069189, "dur": 33, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961069225, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961069227, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961069255, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961069258, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961069303, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961069305, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961069357, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961069386, "dur": 82, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961069471, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961069476, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961069510, "dur": 881, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961070394, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961070395, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961070434, "dur": 2, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961070437, "dur": 20, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961070460, "dur": 68, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961070531, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961070554, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961070574, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961070596, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961070632, "dur": 192, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961070826, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961070855, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961070891, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961070935, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961070987, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961071020, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961071023, "dur": 28, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961071054, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961071056, "dur": 187, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961071247, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961071273, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961071294, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961071321, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961071357, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961071359, "dur": 551, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961071916, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961071961, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961071963, "dur": 378, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961072345, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961072372, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961072397, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961072433, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961072468, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961072496, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961072523, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961072564, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961072566, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961072611, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961072645, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961072678, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961072712, "dur": 96, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961072812, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961072842, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961072872, "dur": 511, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961073386, "dur": 43, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961073432, "dur": 35, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961073470, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961073472, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961073504, "dur": 163, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961073671, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961073675, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961073718, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961073722, "dur": 40, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961073767, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961073797, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961073841, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961073888, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961073890, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961073931, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961073933, "dur": 113, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961074051, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961074081, "dur": 88, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961074171, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961074193, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961074215, "dur": 66, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961074286, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961074316, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961074358, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961074360, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961074393, "dur": 248, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961077672, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961077676, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961077736, "dur": 2, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961077739, "dur": 401005, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961478754, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961478757, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961478804, "dur": 1340, "ph": "X", "name": "ProcessMessages 205", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961480146, "dur": 9511, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961489664, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961489667, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961489691, "dur": 257, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961489952, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961489995, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961489997, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961490030, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961490032, "dur": 71, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961490106, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961490108, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961490155, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961490157, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961490204, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961490241, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961490275, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961490305, "dur": 69, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961490378, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961490407, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961490431, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961490474, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961490476, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961490517, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961490562, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961490563, "dur": 49, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961490617, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961490619, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961490655, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961490657, "dur": 164, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961490826, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961490861, "dur": 2683, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961493549, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961493588, "dur": 1228, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961494820, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961494851, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961494854, "dur": 259, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961495116, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961495165, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961495201, "dur": 106, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961495312, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961495359, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961495392, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961495434, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961495435, "dur": 30, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961495468, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961495470, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961495496, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961495519, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961495568, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961495599, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961495600, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961495629, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961495664, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961495684, "dur": 73, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961495761, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961495790, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961495816, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961495841, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961495868, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961495892, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961495919, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961495949, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961495985, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961495987, "dur": 33, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961496022, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961496024, "dur": 28, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961496056, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961496106, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961496108, "dur": 49, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961496160, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961496162, "dur": 38, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961496203, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961496205, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961496240, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961496242, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961496276, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961496278, "dur": 22, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961496304, "dur": 30, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961496337, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961496383, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961496424, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961496455, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961496457, "dur": 26, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961496486, "dur": 26, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961496515, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961496516, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961496550, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961496551, "dur": 40, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961496594, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961496596, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961496634, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961496636, "dur": 29, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961496669, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961496671, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961496704, "dur": 29, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961496735, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961496737, "dur": 21, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961496762, "dur": 22, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961496786, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961496837, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961496883, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961496885, "dur": 28, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961496916, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961496950, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961496952, "dur": 501, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961497457, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961497498, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961497500, "dur": 54, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961497558, "dur": 32, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961497592, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961497595, "dur": 539, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961498139, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961498175, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961498216, "dur": 544, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961498763, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961498804, "dur": 37, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961498845, "dur": 29, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961498877, "dur": 29, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961498909, "dur": 25, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961498937, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961498962, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961498994, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961499023, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961499055, "dur": 74, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961499134, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961499158, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961499182, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961499213, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961499249, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961499250, "dur": 44, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961499298, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961499301, "dur": 727, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961500031, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961500033, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961500064, "dur": 266, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 19816, "tid": 12884901888, "ts": 1755896961500332, "dur": 9000, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 19816, "tid": 12, "ts": 1755896961524376, "dur": 1444, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 19816, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 19816, "tid": 8589934592, "ts": 1755896961000192, "dur": 133558, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 19816, "tid": 8589934592, "ts": 1755896961133753, "dur": 4, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 19816, "tid": 8589934592, "ts": 1755896961133758, "dur": 1577, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 19816, "tid": 12, "ts": 1755896961525822, "dur": 3, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 19816, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 19816, "tid": 4294967296, "ts": 1755896960972792, "dur": 538032, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 19816, "tid": 4294967296, "ts": 1755896960978481, "dur": 15992, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 19816, "tid": 4294967296, "ts": 1755896961510990, "dur": 5438, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 19816, "tid": 4294967296, "ts": 1755896961513356, "dur": 1921, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 19816, "tid": 4294967296, "ts": 1755896961516489, "dur": 8, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 19816, "tid": 12, "ts": 1755896961525827, "dur": 5, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1755896961005797, "dur": 15831, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755896961021638, "dur": 844, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755896961022603, "dur": 63, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1755896961022667, "dur": 1115, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755896961023871, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_30460E0C3839B364.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1755896961024321, "dur": 108, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_4C8A9319ED1CA3AE.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1755896961024981, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_71130C2744ADE79E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1755896961031775, "dur": 2136, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1755896961035406, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1755896961042778, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1755896961044293, "dur": 231, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.GPUDriven.Runtime.dll"}}, {"pid": 12345, "tid": 0, "ts": 1755896961044998, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1755896961023811, "dur": 23508, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755896961047331, "dur": 452431, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755896961499763, "dur": 384, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755896961500448, "dur": 1384, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1755896961023244, "dur": 24099, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755896961047375, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755896961047485, "dur": 642, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_4F4FD9FF61AF6F28.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755896961048128, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755896961048228, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_8065517B5A536D03.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755896961048326, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_8065517B5A536D03.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755896961048484, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_36F2911304CC020F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755896961048690, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_1C4A8B1B6898FD4D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755896961048851, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755896961048910, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_1C4A8B1B6898FD4D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755896961048982, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755896961049043, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755896961049199, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1755896961049355, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755896961049542, "dur": 179, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755896961049725, "dur": 14765, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1755896961064491, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755896961064759, "dur": 1423, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755896961066182, "dur": 747, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755896961066936, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1755896961067096, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755896961067153, "dur": 875, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1755896961068029, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755896961068195, "dur": 417, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755896961068636, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755896961068699, "dur": 186, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755896961068885, "dur": 880, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755896961069765, "dur": 585, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755896961070350, "dur": 1196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755896961071546, "dur": 4472, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755896961076018, "dur": 59741, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755896961137863, "dur": 242, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.2.1f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 1, "ts": 1755896961138105, "dur": 1040, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.2.1f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 1, "ts": 1755896961139145, "dur": 95, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.2.1f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 1, "ts": 1755896961135760, "dur": 3487, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755896961139247, "dur": 346711, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755896961485993, "dur": 5089, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1755896961491083, "dur": 2874, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755896961493966, "dur": 4726, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1755896961498693, "dur": 595, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1755896961499343, "dur": 512, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755896961023530, "dur": 23988, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755896961047528, "dur": 624, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_2731044146C71882.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755896961048354, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_2CD26214566796B4.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755896961048419, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755896961048488, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_448E988461C74DB0.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755896961048687, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_94137FF513C48ABF.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755896961048880, "dur": 165, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_B9DC0DB8A04DAC4C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755896961049094, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755896961049219, "dur": 159, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1755896961049384, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1755896961049815, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1755896961049972, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755896961050307, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11294775302662683298.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1755896961050541, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15394042617203071315.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1755896961050658, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15394042617203071315.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1755896961050798, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755896961050874, "dur": 1388, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755896961052262, "dur": 959, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755896961053222, "dur": 897, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755896961054119, "dur": 996, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755896961055116, "dur": 1158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755896961056274, "dur": 836, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755896961057111, "dur": 901, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755896961058013, "dur": 907, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755896961058921, "dur": 864, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755896961059786, "dur": 869, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755896961060655, "dur": 863, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755896961061518, "dur": 853, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755896961062372, "dur": 873, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755896961063245, "dur": 869, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755896961064114, "dur": 87, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755896961064273, "dur": 94, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755896961064436, "dur": 301, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755896961064738, "dur": 1285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755896961066024, "dur": 879, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755896961066904, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755896961067028, "dur": 278, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755896961067311, "dur": 1960, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1755896961069272, "dur": 369, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755896961069737, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755896961070012, "dur": 1164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1755896961071177, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755896961071284, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755896961071414, "dur": 1426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1755896961072841, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755896961072967, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1755896961073122, "dur": 357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1755896961073479, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755896961073590, "dur": 2463, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755896961076054, "dur": 409936, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755896961485992, "dur": 4166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1755896961490159, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755896961490369, "dur": 5031, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1755896961495401, "dur": 388, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755896961495796, "dur": 3716, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1755896961499514, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1755896961499673, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755896961023279, "dur": 24085, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755896961047379, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755896961047460, "dur": 554, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_5A472186080A5AB1.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755896961048057, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_D2ABA6F656B34D47.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755896961048126, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755896961048191, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_886CDD09B49D7AF9.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755896961048251, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755896961048316, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_A964C83E710562E0.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755896961048374, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755896961048434, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_9CCBC171CB8C01C8.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755896961048486, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755896961048650, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_AF9E01432509B98A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755896961048716, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755896961048772, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_943D1566EB36197C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755896961048872, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_943D1566EB36197C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755896961048989, "dur": 159, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1755896961049202, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1755896961049445, "dur": 261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1755896961049707, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755896961049835, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755896961049900, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1755896961050003, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1755896961050084, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1755896961050231, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755896961050282, "dur": 295, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.Entities.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1755896961050669, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17296387151066238333.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1755896961050883, "dur": 1110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755896961051994, "dur": 872, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755896961052866, "dur": 970, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755896961053836, "dur": 825, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755896961054662, "dur": 1292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755896961055955, "dur": 1001, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755896961056957, "dur": 984, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755896961057942, "dur": 917, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755896961058860, "dur": 855, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755896961059715, "dur": 848, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755896961060563, "dur": 937, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755896961061911, "dur": 1336, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@be6c4fd0abf5\\InputSystem\\Editor\\Analytics\\InputExitPlayModeAnalytic.cs"}}, {"pid": 12345, "tid": 3, "ts": 1755896961063470, "dur": 851, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@be6c4fd0abf5\\InputSystem\\Devices\\TrackedDevice.cs"}}, {"pid": 12345, "tid": 3, "ts": 1755896961061501, "dur": 3056, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755896961064557, "dur": 179, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755896961064737, "dur": 1285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755896961066086, "dur": 969, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755896961067065, "dur": 395, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755896961067461, "dur": 651, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755896961068124, "dur": 738, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1755896961068863, "dur": 697, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755896961069617, "dur": 132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755896961069750, "dur": 579, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755896961070330, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1755896961070515, "dur": 363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1755896961070878, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755896961070991, "dur": 452, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755896961071443, "dur": 134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755896961071577, "dur": 4457, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755896961076034, "dur": 413992, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755896961490031, "dur": 5418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1755896961495451, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755896961495538, "dur": 3472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1755896961499011, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1755896961499254, "dur": 739, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755896961023337, "dur": 24055, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755896961047403, "dur": 362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_D95410EB09159078.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755896961047803, "dur": 193, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_D95410EB09159078.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755896961048044, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755896961048123, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_558CAE984AE1D1A5.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755896961048408, "dur": 118, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_88D8DDE2BF366D61.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755896961048528, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_BB73E5A042403DAF.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755896961048685, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755896961048843, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_30460E0C3839B364.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755896961049123, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1755896961049378, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755896961049470, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755896961049555, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1755896961049705, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755896961049821, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755896961049916, "dur": 262, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1755896961050218, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755896961050284, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1755896961050393, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18218310762646611085.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1755896961050646, "dur": 175, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18218310762646611085.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1755896961050822, "dur": 1020, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755896961051843, "dur": 665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755896961052508, "dur": 891, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755896961053399, "dur": 842, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755896961054241, "dur": 1377, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755896961055618, "dur": 865, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755896961056483, "dur": 1151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755896961057634, "dur": 853, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755896961058487, "dur": 864, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755896961059352, "dur": 868, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755896961060221, "dur": 1044, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755896961061265, "dur": 869, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755896961062134, "dur": 871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755896961063006, "dur": 920, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755896961063927, "dur": 857, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755896961064785, "dur": 1366, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755896961066151, "dur": 822, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755896961066984, "dur": 257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755896961067298, "dur": 1135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1755896961068434, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755896961068669, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755896961068927, "dur": 1238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1755896961070166, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755896961070335, "dur": 1024, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755896961071360, "dur": 142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755896961071503, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1755896961071670, "dur": 407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1755896961072078, "dur": 234, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755896961072365, "dur": 3679, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755896961076044, "dur": 409931, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755896961485977, "dur": 4863, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1755896961490841, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755896961490937, "dur": 5743, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1755896961496682, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755896961496958, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755896961497063, "dur": 195, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Config.Runtime.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1755896961497259, "dur": 1935, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1755896961499322, "dur": 484, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755896961023372, "dur": 24035, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755896961047417, "dur": 588, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_4297D6ED2C7F7138.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755896961048059, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_47FD15CC38401525.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755896961048123, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755896961048236, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_AF06999FBA4544CA.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755896961048332, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_AF06999FBA4544CA.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755896961048394, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_3FD29ACDA5717940.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755896961048513, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_88B5229BB905F051.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755896961048914, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_8189EC8801BEEB18.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755896961049008, "dur": 172, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_8189EC8801BEEB18.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755896961049189, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755896961049264, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1755896961049332, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755896961049402, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1755896961049673, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755896961049998, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1755896961050097, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1755896961050210, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755896961050440, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10411718816959651794.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1755896961050589, "dur": 346, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10411718816959651794.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1755896961050936, "dur": 1089, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755896961052026, "dur": 974, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755896961053001, "dur": 947, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755896961053949, "dur": 876, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755896961054825, "dur": 1191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755896961056017, "dur": 833, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755896961056850, "dur": 882, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755896961057732, "dur": 865, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755896961058597, "dur": 869, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755896961059467, "dur": 871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755896961060338, "dur": 926, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755896961061265, "dur": 830, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755896961062095, "dur": 843, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755896961062938, "dur": 931, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755896961063870, "dur": 929, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755896961064799, "dur": 1334, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755896961066133, "dur": 866, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755896961067007, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755896961067259, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755896961067430, "dur": 1023, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1755896961068454, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755896961068660, "dur": 195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755896961068856, "dur": 878, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755896961069735, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755896961070020, "dur": 421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1755896961070442, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755896961070520, "dur": 866, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755896961071386, "dur": 115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755896961071502, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1755896961071753, "dur": 565, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1755896961072319, "dur": 429, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755896961072798, "dur": 3252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755896961076051, "dur": 413992, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755896961490044, "dur": 5317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1755896961495362, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755896961495606, "dur": 3917, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1755896961499524, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1755896961499644, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755896961023580, "dur": 23963, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755896961047552, "dur": 672, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_7D055410D6C309E4.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755896961048277, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_1CF8F8636ADD1698.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755896961048507, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_F129D49E2ADECCC2.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755896961048823, "dur": 281, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_C25CFDCCBD0E71F0.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755896961049155, "dur": 260, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1755896961049572, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755896961049630, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755896961049766, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755896961049978, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1755896961050225, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755896961050305, "dur": 220, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1755896961050526, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12900333509587573630.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1755896961050667, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12900333509587573630.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1755896961050825, "dur": 1055, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755896961051880, "dur": 863, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755896961052744, "dur": 929, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755896961053673, "dur": 871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755896961054544, "dur": 1247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755896961055792, "dur": 846, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755896961057230, "dur": 3461, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.rendering.light-transport@2c9279f90d7c\\Runtime\\UnifiedRayTracing\\UnifiedRayTracingException.cs"}}, {"pid": 12345, "tid": 6, "ts": 1755896961056639, "dur": 4262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755896961060902, "dur": 851, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755896961061753, "dur": 870, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755896961062624, "dur": 1120, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755896961063744, "dur": 936, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755896961064681, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755896961064782, "dur": 1385, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755896961066167, "dur": 782, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755896961066957, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755896961067209, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755896961067268, "dur": 928, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1755896961068197, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755896961068446, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755896961068506, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1755896961068846, "dur": 646, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1755896961069492, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755896961069626, "dur": 115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755896961069741, "dur": 166, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755896961069912, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755896961069974, "dur": 378, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755896961070353, "dur": 1111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755896961071465, "dur": 88, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755896961071553, "dur": 4477, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755896961076030, "dur": 409957, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755896961485988, "dur": 4329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1755896961490318, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755896961490517, "dur": 5221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1755896961495739, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755896961495885, "dur": 3403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1755896961499289, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1755896961499401, "dur": 506, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755896961023615, "dur": 23938, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755896961047564, "dur": 703, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_40502E6DCAE34EBD.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755896961048416, "dur": 157, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_71130C2744ADE79E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755896961048575, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_C1731F7A9273BA75.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755896961048863, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_9ADCC66C60352D2B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755896961048977, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_097DDEAFECA28810.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755896961049207, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_F5791B14F366A51E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755896961049324, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755896961049568, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755896961049851, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1755896961049969, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755896961050082, "dur": 132, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1755896961050417, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10142702499866438521.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1755896961050594, "dur": 204, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10142702499866438521.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1755896961050868, "dur": 137, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15183149355271759364.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1755896961051006, "dur": 1110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755896961052117, "dur": 883, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755896961053001, "dur": 927, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755896961053929, "dur": 919, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755896961055283, "dur": 1649, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.State\\Transitions\\NesterStateTransitionAnalyser.cs"}}, {"pid": 12345, "tid": 7, "ts": 1755896961054848, "dur": 2463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755896961057312, "dur": 946, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755896961058259, "dur": 852, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755896961059112, "dur": 857, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755896961059970, "dur": 932, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755896961060902, "dur": 848, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755896961061751, "dur": 869, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755896961062621, "dur": 1117, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755896961063738, "dur": 939, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755896961064678, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755896961064740, "dur": 1451, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755896961066191, "dur": 729, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755896961066928, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755896961067083, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755896961067141, "dur": 889, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1755896961068031, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755896961068244, "dur": 369, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755896961068614, "dur": 73, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755896961068687, "dur": 176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755896961068863, "dur": 892, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755896961069755, "dur": 576, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755896961070331, "dur": 957, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755896961071290, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755896961071454, "dur": 1511, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1755896961072966, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755896961073080, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755896961073224, "dur": 572, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1755896961073796, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755896961073917, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755896961074076, "dur": 438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1755896961074515, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755896961074631, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1755896961074778, "dur": 302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1755896961075080, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755896961075244, "dur": 778, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755896961076023, "dur": 409928, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755896961485953, "dur": 4951, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1755896961490905, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755896961491023, "dur": 4691, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1755896961495715, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755896961496099, "dur": 451, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755896961496561, "dur": 396, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755896961496967, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755896961497100, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755896961497189, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755896961497266, "dur": 2013, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1755896961499317, "dur": 633, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755896961023463, "dur": 24023, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755896961047497, "dur": 625, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_D69F050D9A0ED123.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755896961048217, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755896961048312, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_AFDD633454108E81.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755896961048374, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755896961048490, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755896961048654, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_EB9810B219936736.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755896961048912, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1755896961049110, "dur": 185, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1755896961049299, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755896961049384, "dur": 121, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1755896961049685, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1755896961049793, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755896961049863, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755896961049968, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1755896961050125, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755896961050186, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755896961050330, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2157608619508796868.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1755896961050497, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2157608619508796868.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1755896961050548, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1488387367365330867.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1755896961050668, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1488387367365330867.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1755896961050888, "dur": 1463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755896961052352, "dur": 861, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755896961053213, "dur": 881, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755896961054094, "dur": 910, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755896961055005, "dur": 1166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755896961056172, "dur": 1122, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755896961057295, "dur": 892, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755896961058187, "dur": 866, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755896961059053, "dur": 843, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755896961059896, "dur": 1071, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755896961060968, "dur": 834, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755896961061889, "dur": 769, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@8017b507cc74\\Unity.Mathematics\\uint3x4.gen.cs"}}, {"pid": 12345, "tid": 8, "ts": 1755896961061803, "dur": 1643, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755896961063466, "dur": 602, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@80da3b035d6b\\Runtime\\UGUI\\EventSystem\\Raycasters\\Physics2DRaycaster.cs"}}, {"pid": 12345, "tid": 8, "ts": 1755896961063447, "dur": 626, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755896961064073, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755896961064521, "dur": 217, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755896961064739, "dur": 1281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755896961066065, "dur": 1030, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755896961067095, "dur": 1509, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755896961068656, "dur": 195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755896961068852, "dur": 724, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755896961069577, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1755896961069799, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755896961069956, "dur": 610, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1755896961070567, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755896961070672, "dur": 704, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755896961071376, "dur": 249, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755896961071626, "dur": 4418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755896961076045, "dur": 409988, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755896961486035, "dur": 4568, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1755896961490604, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755896961490801, "dur": 6046, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1755896961496848, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755896961497030, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755896961497248, "dur": 632, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755896961497887, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755896961497947, "dur": 1670, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1755896961499659, "dur": 233, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755896961023513, "dur": 23988, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755896961047511, "dur": 637, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_7CE7E2E85C9B872F.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755896961048149, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755896961048216, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_3AD12250A59F4CEE.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755896961048337, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_6583F22C0A896B6F.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755896961048389, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755896961048468, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_0B128FF1BBAAC47E.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755896961048669, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_4C8A9319ED1CA3AE.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755896961049004, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_1B42BC8AE274CFDA.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755896961049211, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_59627543C51E81A1.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755896961049289, "dur": 328, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_59627543C51E81A1.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755896961049760, "dur": 179, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1755896961049999, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1755896961050112, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1755896961050293, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1755896961050444, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755896961050496, "dur": 213, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13909235412005675431.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1755896961050769, "dur": 203, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7759033573704817190.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1755896961052087, "dur": 1128, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.2.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Http.Results.dll"}}, {"pid": 12345, "tid": 9, "ts": 1755896961050972, "dur": 2379, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755896961053352, "dur": 899, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755896961054252, "dur": 911, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755896961055163, "dur": 1182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755896961056346, "dur": 875, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755896961057222, "dur": 920, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755896961058336, "dur": 3178, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\Plugins\\IPluginModule.cs"}}, {"pid": 12345, "tid": 9, "ts": 1755896961058143, "dur": 4106, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755896961062249, "dur": 864, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755896961063113, "dur": 919, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755896961064032, "dur": 975, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755896961065007, "dur": 1032, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755896961066040, "dur": 1086, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755896961067126, "dur": 1488, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755896961068614, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755896961068673, "dur": 187, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755896961068861, "dur": 892, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755896961069754, "dur": 573, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755896961070329, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1755896961070518, "dur": 407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1755896961070926, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755896961071037, "dur": 523, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755896961071561, "dur": 4467, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755896961076028, "dur": 409944, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755896961485974, "dur": 4322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1755896961490296, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755896961490392, "dur": 5123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1755896961495521, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755896961495611, "dur": 3580, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1755896961499192, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1755896961499360, "dur": 424, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755896961023309, "dur": 24067, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755896961047386, "dur": 558, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_574DE5121CC9D879.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755896961047974, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_574DE5121CC9D879.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755896961048066, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_E57DD11F6513DD1B.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755896961048210, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755896961048516, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_7E5B9059DDF5C58D.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755896961048671, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755896961048939, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_D01CC5A6D0F5DAF9.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1755896961049142, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755896961049211, "dur": 230, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1755896961049443, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1755896961049609, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755896961049707, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755896961049786, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755896961050164, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755896961050282, "dur": 142, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1755896961050575, "dur": 420, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10831531723267835799.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1755896961050998, "dur": 1466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755896961052465, "dur": 1014, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755896961053480, "dur": 837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755896961054317, "dur": 1332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755896961055650, "dur": 840, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755896961056490, "dur": 1054, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755896961057544, "dur": 856, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755896961058401, "dur": 896, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755896961059297, "dur": 863, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755896961060161, "dur": 1004, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755896961061166, "dur": 918, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755896961062084, "dur": 844, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755896961062928, "dur": 1077, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755896961064005, "dur": 851, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755896961064856, "dur": 1194, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755896961066050, "dur": 1089, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755896961067140, "dur": 1470, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755896961068635, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755896961068711, "dur": 166, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755896961068878, "dur": 880, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755896961069758, "dur": 583, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755896961070341, "dur": 1015, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755896961071405, "dur": 197, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755896961071602, "dur": 4434, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755896961076037, "dur": 409959, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755896961485999, "dur": 4314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1755896961490315, "dur": 366, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755896961490688, "dur": 5404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1755896961496093, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755896961496311, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755896961496404, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755896961496570, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.dll"}}, {"pid": 12345, "tid": 10, "ts": 1755896961496674, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755896961496744, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755896961496942, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1755896961496993, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.2D.Runtime.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1755896961497129, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755896961497255, "dur": 1305, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1755896961498613, "dur": 1139, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755896961023551, "dur": 23980, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755896961047540, "dur": 657, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_80D3DD5FCF7A4155.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755896961048248, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_20E9E08310ABADB8.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755896961048343, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_20E9E08310ABADB8.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755896961048450, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_6A4766F7572387DA.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755896961048624, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755896961048890, "dur": 445, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_E4EBE1DEA4B9DBF9.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755896961049360, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755896961049503, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755896961049561, "dur": 14976, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1755896961064538, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755896961064808, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755896961064984, "dur": 860, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1755896961065845, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755896961066084, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755896961066246, "dur": 440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1755896961066686, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755896961066907, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755896961067111, "dur": 1160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1755896961068273, "dur": 252, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755896961068542, "dur": 234, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755896961068870, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755896961069187, "dur": 697, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1755896961069884, "dur": 344, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755896961070356, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755896961070581, "dur": 664, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1755896961071245, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755896961071506, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755896961071743, "dur": 1023, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1755896961072767, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755896961072892, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755896961073020, "dur": 591, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1755896961073612, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755896961073730, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1755896961073859, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1755896961074195, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755896961074319, "dur": 1746, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755896961076065, "dur": 409899, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755896961485966, "dur": 4868, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1755896961490835, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755896961491067, "dur": 5547, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1755896961496615, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755896961496714, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755896961497315, "dur": 2057, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1755896961499461, "dur": 514, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755896961023417, "dur": 24016, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755896961047447, "dur": 620, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_324EC462D42B6AD5.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755896961048109, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_B76ACB483990A7B3.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755896961048267, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_128BE6A580A94DC6.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755896961048476, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_253848F44BDD5E17.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755896961048674, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_446E57718A74AE8B.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755896961048750, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755896961048941, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_673AEEFA91C338F1.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755896961049129, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1755896961049277, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755896961049413, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755896961049473, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755896961049973, "dur": 133, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1755896961050108, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1755896961050179, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755896961050479, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5803490648119114145.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1755896961050574, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8863518860715653438.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1755896961050702, "dur": 149, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8863518860715653438.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1755896961050852, "dur": 1131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755896961051984, "dur": 900, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755896961052885, "dur": 871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755896961053756, "dur": 858, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755896961055288, "dur": 1455, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@844e9247af82\\Editor\\Data\\Graphs\\ShaderGraphRequirements.cs"}}, {"pid": 12345, "tid": 12, "ts": 1755896961054615, "dur": 2349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755896961056965, "dur": 855, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755896961057820, "dur": 903, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755896961058724, "dur": 858, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755896961059583, "dur": 883, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755896961060632, "dur": 1282, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Core\\Cloning\\CloningContext.cs"}}, {"pid": 12345, "tid": 12, "ts": 1755896961060466, "dur": 2103, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755896961062569, "dur": 888, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755896961063457, "dur": 938, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755896961064434, "dur": 301, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755896961064735, "dur": 1290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755896961066025, "dur": 875, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755896961066902, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755896961067079, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755896961067146, "dur": 1146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1755896961068292, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755896961068441, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755896961068500, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755896961068770, "dur": 692, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1755896961069463, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755896961069605, "dur": 455, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755896961070063, "dur": 295, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755896961070359, "dur": 1096, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755896961071456, "dur": 113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755896961071569, "dur": 4444, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755896961076015, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1755896961076251, "dur": 409726, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755896961485981, "dur": 4391, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1755896961490373, "dur": 592, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755896961490973, "dur": 5136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1755896961496110, "dur": 278, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755896961496532, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755896961496690, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755896961496949, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755896961497068, "dur": 178, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1755896961497249, "dur": 657, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755896961497980, "dur": 1649, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1755896961499683, "dur": 149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755896961023428, "dur": 24035, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755896961047476, "dur": 644, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_47278636E8915295.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1755896961048121, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755896961048182, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_F270133E58124F18.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1755896961048341, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_43979216E7A92FE9.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1755896961048404, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755896961048464, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_076091C3DFC2A810.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1755896961048605, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755896961048662, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_D83BBE6D171D2E8B.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1755896961048814, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_B03107865E80E25E.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1755896961048932, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_4D1ACBA4E8E3A92B.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1755896961048984, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755896961049159, "dur": 239, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1755896961049400, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1755896961049736, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755896961049835, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755896961049991, "dur": 152, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1755896961050146, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755896961050208, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755896961050265, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755896961050427, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14832617886295879631.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1755896961050631, "dur": 255, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4772145154433932943.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1755896961050887, "dur": 1194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755896961052082, "dur": 872, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755896961052955, "dur": 953, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755896961053908, "dur": 840, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755896961054748, "dur": 1305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755896961056053, "dur": 833, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755896961056886, "dur": 944, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755896961057831, "dur": 905, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755896961058737, "dur": 858, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755896961059595, "dur": 887, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755896961060483, "dur": 844, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755896961061328, "dur": 920, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755896961062249, "dur": 860, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755896961063110, "dur": 881, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755896961063992, "dur": 858, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755896961064850, "dur": 1217, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755896961066068, "dur": 1014, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755896961067082, "dur": 1527, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755896961068634, "dur": 208, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755896961068843, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1755896961069067, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755896961069161, "dur": 760, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1755896961069921, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755896961070042, "dur": 330, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755896961070372, "dur": 1022, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755896961071395, "dur": 238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755896961071634, "dur": 4407, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755896961076041, "dur": 409904, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755896961485948, "dur": 4262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1755896961490211, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755896961490436, "dur": 5265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1755896961495702, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755896961495816, "dur": 3705, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1755896961499522, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1755896961499668, "dur": 200, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755896961023652, "dur": 23918, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755896961047581, "dur": 686, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_3A6F5DE8BB04652D.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1755896961048321, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_F15DE0412BCD77BB.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1755896961048475, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755896961048665, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_C223B6F3143702D1.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1755896961048953, "dur": 137, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_18E07CEE712070C7.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1755896961049169, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1755896961049362, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1755896961049424, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1755896961049689, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755896961049843, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755896961049921, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755896961049972, "dur": 376, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1755896961050350, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4014436084419441659.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1755896961050661, "dur": 323, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3388064732626934676.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1755896961050984, "dur": 1157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755896961052142, "dur": 898, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755896961053041, "dur": 899, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755896961053941, "dur": 874, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755896961054815, "dur": 1307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755896961056122, "dur": 881, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755896961057003, "dur": 873, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755896961057876, "dur": 968, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755896961058844, "dur": 876, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755896961059721, "dur": 860, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755896961060582, "dur": 861, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755896961061444, "dur": 847, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755896961062292, "dur": 866, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755896961063159, "dur": 878, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755896961064038, "dur": 1018, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755896961065057, "dur": 972, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755896961066029, "dur": 1125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755896961067154, "dur": 1451, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755896961068608, "dur": 528, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755896961069142, "dur": 633, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755896961069776, "dur": 570, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755896961070346, "dur": 1183, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755896961071529, "dur": 4487, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755896961076017, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1755896961076242, "dur": 409770, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755896961486016, "dur": 4526, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1755896961490543, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755896961490627, "dur": 4846, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1755896961495474, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755896961495584, "dur": 3464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1755896961499049, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1755896961499217, "dur": 534, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755896961023692, "dur": 23895, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755896961047600, "dur": 746, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_C5706F0A31B0C16B.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1755896961048347, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755896961048433, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_35E2FC16174B6AD9.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1755896961048794, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_C124AD4B97AA3982.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1755896961048907, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755896961048964, "dur": 368, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1755896961049369, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755896961049637, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1755896961049765, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755896961049861, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755896961049965, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1755896961050305, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3064155424177982801.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1755896961050403, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9870516708743257357.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1755896961050494, "dur": 343, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9870516708743257357.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1755896961050838, "dur": 1189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755896961052027, "dur": 902, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755896961052929, "dur": 909, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755896961053839, "dur": 828, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755896961054668, "dur": 1215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755896961055884, "dur": 1187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755896961057072, "dur": 896, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755896961057969, "dur": 909, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755896961058878, "dur": 871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755896961059749, "dur": 871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755896961060621, "dur": 876, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755896961061497, "dur": 866, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755896961062364, "dur": 879, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755896961063244, "dur": 879, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755896961064368, "dur": 107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755896961064475, "dur": 258, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755896961064785, "dur": 1375, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755896961066160, "dur": 800, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755896961066967, "dur": 418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1755896961067386, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755896961067629, "dur": 1035, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1755896961068665, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755896961068930, "dur": 848, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755896961069778, "dur": 573, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755896961070351, "dur": 1169, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755896961071520, "dur": 2406, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755896961073928, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1755896961074063, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755896961074127, "dur": 416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1755896961074543, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755896961074639, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755896961074703, "dur": 1354, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755896961076057, "dur": 409905, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755896961485966, "dur": 4460, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1755896961490427, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755896961490668, "dur": 5538, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1755896961496207, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755896961496404, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipeline.Universal.ShaderLibrary.dll"}}, {"pid": 12345, "tid": 15, "ts": 1755896961496461, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755896961496590, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755896961496666, "dur": 340, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755896961497097, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755896961497220, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755896961497280, "dur": 2003, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1755896961499284, "dur": 651, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755896961023728, "dur": 23876, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755896961047616, "dur": 672, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_D443F0926FAEB623.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1755896961048333, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_D443F0926FAEB623.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1755896961048500, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_3352A694B6772687.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1755896961048654, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755896961048716, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_6B03EF9A2B2BFBD3.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1755896961048863, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_6B03EF9A2B2BFBD3.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1755896961048936, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_28DAF9479429A673.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1755896961049164, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755896961049221, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1755896961049311, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755896961049538, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1755896961049711, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755896961049809, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1755896961049961, "dur": 176, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1755896961050140, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755896961050219, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755896961050287, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1755896961050499, "dur": 286, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1755896961050870, "dur": 145, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15312589170787849123.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1755896961051016, "dur": 1356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755896961052373, "dur": 864, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755896961053238, "dur": 859, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755896961054098, "dur": 853, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755896961054999, "dur": 655, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.State\\Plugin\\Migrations\\Migration_1_5_1_to_1_5_2.cs"}}, {"pid": 12345, "tid": 16, "ts": 1755896961054952, "dur": 1503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755896961056455, "dur": 861, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755896961057317, "dur": 906, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755896961058224, "dur": 880, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755896961059104, "dur": 906, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755896961060010, "dur": 910, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755896961060921, "dur": 853, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755896961061775, "dur": 847, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755896961062623, "dur": 937, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755896961063560, "dur": 931, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755896961064492, "dur": 346, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755896961064838, "dur": 1247, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755896961066085, "dur": 983, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755896961067077, "dur": 346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1755896961067423, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755896961067489, "dur": 969, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1755896961068459, "dur": 310, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755896961068836, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1755896961069060, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755896961069119, "dur": 661, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1755896961069781, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755896961069940, "dur": 408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755896961070348, "dur": 1189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755896961071537, "dur": 4482, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755896961076020, "dur": 63234, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755896961139256, "dur": 346711, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755896961485969, "dur": 4637, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1755896961490607, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755896961490698, "dur": 5839, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1755896961496538, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755896961496962, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755896961497081, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755896961497217, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755896961497294, "dur": 2015, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1755896961499309, "dur": 653, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755896961023772, "dur": 23847, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755896961047628, "dur": 686, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_F3E222EC1821C3F6.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1755896961048359, "dur": 130, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_F3E222EC1821C3F6.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1755896961048491, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_4CE88DA2C6FA0DDC.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1755896961048659, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755896961048741, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_12BCE5693E524B55.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1755896961048992, "dur": 150, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 17, "ts": 1755896961049151, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755896961049203, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.rsp2"}}, {"pid": 12345, "tid": 17, "ts": 1755896961049308, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755896961049367, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1755896961049470, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755896961049584, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1755896961049691, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755896961049977, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp2"}}, {"pid": 12345, "tid": 17, "ts": 1755896961050079, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1755896961050430, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13686715218353603589.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1755896961050612, "dur": 257, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13686715218353603589.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1755896961050870, "dur": 1268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755896961052139, "dur": 1123, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755896961053263, "dur": 937, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755896961054201, "dur": 856, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755896961055058, "dur": 1252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755896961056311, "dur": 981, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755896961057292, "dur": 878, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755896961058171, "dur": 871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755896961059043, "dur": 865, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755896961059908, "dur": 883, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755896961060791, "dur": 830, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755896961061621, "dur": 861, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755896961062482, "dur": 905, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755896961063388, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755896961063699, "dur": 979, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755896961064678, "dur": 94, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755896961064772, "dur": 1403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755896961066175, "dur": 764, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755896961066946, "dur": 302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1755896961067295, "dur": 893, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1755896961068188, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755896961068362, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1755896961068626, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755896961068682, "dur": 614, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1755896961069297, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755896961069418, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1755896961069587, "dur": 531, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1755896961070118, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755896961070239, "dur": 121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755896961070360, "dur": 1073, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755896961071433, "dur": 152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755896961071585, "dur": 4441, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755896961076026, "dur": 409928, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755896961485957, "dur": 4738, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1755896961490696, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755896961490904, "dur": 6322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1755896961497226, "dur": 1311, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1755896961498599, "dur": 1160, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755896961023799, "dur": 23833, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755896961047642, "dur": 729, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_D7FCB2E6762B3FB1.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1755896961048416, "dur": 162, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_D7FCB2E6762B3FB1.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1755896961048579, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_798AE90902F00157.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1755896961049095, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1755896961049247, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1755896961049332, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755896961049384, "dur": 185, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1755896961049696, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755896961049878, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755896961049965, "dur": 317, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 18, "ts": 1755896961050453, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6947401630772442630.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1755896961050616, "dur": 153, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6947401630772442630.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1755896961050770, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2781082588993088484.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1755896961050908, "dur": 1179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755896961052087, "dur": 925, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755896961053013, "dur": 892, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755896961053906, "dur": 886, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755896961054793, "dur": 1230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755896961056023, "dur": 827, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755896961056850, "dur": 873, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755896961057723, "dur": 939, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755896961058663, "dur": 971, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755896961059634, "dur": 912, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755896961060759, "dur": 1213, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collections@d49facba0036\\Unity.Collections\\NativeRingQueue.cs"}}, {"pid": 12345, "tid": 18, "ts": 1755896961060547, "dur": 2074, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755896961062622, "dur": 929, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755896961063552, "dur": 929, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755896961064482, "dur": 348, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755896961064830, "dur": 1265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755896961066095, "dur": 949, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755896961067052, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1755896961067388, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755896961067449, "dur": 880, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1755896961068330, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755896961068502, "dur": 156, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755896961068658, "dur": 194, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755896961068853, "dur": 883, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755896961069763, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1755896961070061, "dur": 549, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1755896961070611, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755896961070705, "dur": 660, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755896961071366, "dur": 138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755896961071505, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1755896961071689, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755896961071764, "dur": 411, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1755896961072176, "dur": 772, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755896961073008, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1755896961073161, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755896961073261, "dur": 798, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1755896961074060, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755896961074197, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755896961074258, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1755896961074463, "dur": 500, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1755896961074964, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755896961075099, "dur": 959, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755896961076058, "dur": 409941, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755896961486008, "dur": 3944, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1755896961489954, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755896961490072, "dur": 5025, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1755896961495098, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755896961495240, "dur": 4121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1755896961499362, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755896961499426, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1755896961499551, "dur": 268, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755896961023826, "dur": 23823, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755896961047658, "dur": 736, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_21F715593E655655.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1755896961048394, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755896961048458, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConsentModule.dll_3BECFE23642E1E2A.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1755896961048511, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755896961048719, "dur": 260, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConsentModule.dll_3BECFE23642E1E2A.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1755896961048981, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_B3E44AC0B8D941C7.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1755896961049084, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_B3E44AC0B8D941C7.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1755896961049361, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755896961049475, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1755896961049578, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755896961049695, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755896961049763, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755896961049907, "dur": 176, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp2"}}, {"pid": 12345, "tid": 19, "ts": 1755896961050084, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1755896961050167, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755896961050227, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755896961050329, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2897256077774953845.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1755896961050568, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9111662996337956171.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1755896961050657, "dur": 364, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9111662996337956171.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1755896961051022, "dur": 958, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755896961051981, "dur": 898, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755896961052880, "dur": 851, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755896961053731, "dur": 880, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755896961054612, "dur": 1255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755896961055868, "dur": 849, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755896961056718, "dur": 902, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755896961057620, "dur": 915, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755896961058536, "dur": 876, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755896961059413, "dur": 822, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755896961060236, "dur": 924, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755896961061161, "dur": 942, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755896961062104, "dur": 909, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755896961063013, "dur": 920, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755896961063934, "dur": 816, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755896961064811, "dur": 1305, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755896961066116, "dur": 901, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755896961067028, "dur": 514, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1755896961067543, "dur": 435, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755896961067988, "dur": 776, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1755896961068765, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755896961069018, "dur": 744, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755896961069763, "dur": 581, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755896961070345, "dur": 1010, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755896961071409, "dur": 200, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755896961071610, "dur": 4428, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755896961076039, "dur": 409963, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755896961486012, "dur": 4736, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1755896961490748, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755896961490861, "dur": 6941, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1755896961497803, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755896961497971, "dur": 1654, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1755896961499670, "dur": 103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755896961023855, "dur": 23811, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755896961047680, "dur": 756, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_88DF725418823539.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1755896961048490, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_0A94E029953135AC.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1755896961048681, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_DA0ED1A2C90566BA.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1755896961048868, "dur": 202, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_EA0B5218CB807CE8.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1755896961049073, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_283D5FC3BD6A67A2.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1755896961049220, "dur": 124, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp2"}}, {"pid": 12345, "tid": 20, "ts": 1755896961049541, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 20, "ts": 1755896961049640, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755896961049693, "dur": 147, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 20, "ts": 1755896961049896, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1755896961050109, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1755896961050233, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1755896961050379, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4691676790321305727.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1755896961050601, "dur": 347, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2240406767038398906.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1755896961050949, "dur": 1212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755896961052162, "dur": 906, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755896961053114, "dur": 1332, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@facf525283ae\\Editor\\AssetPostProcessors\\PhysicalMaterial3DsMaxPreprocessor.cs"}}, {"pid": 12345, "tid": 20, "ts": 1755896961053068, "dur": 2531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755896961055599, "dur": 847, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755896961056447, "dur": 886, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755896961057334, "dur": 987, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755896961058321, "dur": 857, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755896961059179, "dur": 895, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755896961060074, "dur": 859, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755896961060934, "dur": 893, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755896961062561, "dur": 697, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@8017b507cc74\\Unity.Mathematics\\int3x4.gen.cs"}}, {"pid": 12345, "tid": 20, "ts": 1755896961061828, "dur": 1523, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755896961063351, "dur": 638, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755896961063989, "dur": 865, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755896961064854, "dur": 1205, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755896961066059, "dur": 1050, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755896961067109, "dur": 1499, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755896961068608, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755896961068670, "dur": 189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755896961068859, "dur": 872, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755896961069733, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1755896961069980, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755896961070037, "dur": 551, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1755896961070589, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755896961070857, "dur": 556, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755896961071413, "dur": 180, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755896961071594, "dur": 4438, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755896961076032, "dur": 409949, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755896961486003, "dur": 4596, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1755896961490600, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755896961490719, "dur": 4907, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1755896961495627, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755896961495847, "dur": 3676, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1755896961499524, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1755896961499642, "dur": 240, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755896961023885, "dur": 23799, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755896961047693, "dur": 735, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_FC8457FE0E2359F5.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1755896961048481, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_71DE750A6D09CA7B.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1755896961048677, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_0DAAC2D6CA6D2720.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1755896961048890, "dur": 206, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_0903FEE7BAED8E66.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1755896961049100, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1755896961049296, "dur": 132, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1755896961049466, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755896961049526, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1755896961049776, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1755896961050019, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1755896961050114, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1755896961050230, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755896961050311, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17525389461119239690.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1755896961050421, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755896961050558, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6262281476893245489.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1755896961050671, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6262281476893245489.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1755896961050789, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10292501669419677951.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1755896961050891, "dur": 1633, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755896961052524, "dur": 903, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755896961053428, "dur": 885, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755896961054314, "dur": 905, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755896961055220, "dur": 1185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755896961056406, "dur": 1232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755896961057639, "dur": 944, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755896961058584, "dur": 863, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755896961059447, "dur": 854, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755896961060302, "dur": 864, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755896961061166, "dur": 847, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755896961062014, "dur": 836, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755896961062850, "dur": 879, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755896961063729, "dur": 983, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755896961064739, "dur": 1459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755896961066198, "dur": 712, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755896961066917, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1755896961067108, "dur": 1097, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1755896961068206, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755896961068339, "dur": 355, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755896961068704, "dur": 163, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755896961068867, "dur": 890, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755896961069758, "dur": 578, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755896961070336, "dur": 1022, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755896961071359, "dur": 147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755896961071507, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1755896961071647, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755896961071701, "dur": 636, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1755896961072338, "dur": 460, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755896961072832, "dur": 3215, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755896961076048, "dur": 409960, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755896961486017, "dur": 4993, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 21, "ts": 1755896961491012, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755896961491243, "dur": 5854, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 21, "ts": 1755896961497097, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755896961497187, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755896961497263, "dur": 1948, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1755896961499263, "dur": 721, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755896961023921, "dur": 23775, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755896961047706, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_B9618BB877B2AF9C.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1755896961047804, "dur": 204, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_B9618BB877B2AF9C.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1755896961048047, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755896961048133, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IdentifiersModule.dll_B0863ADC38A40DC9.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1755896961048319, "dur": 239, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_F9E2212BE69EFFE3.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1755896961048559, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_3A57A969F158AF6F.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1755896961048732, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755896961048785, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_3A57A969F158AF6F.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1755896961048852, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755896961048931, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_2A5CE0F0450FED37.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1755896961048995, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755896961049676, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1755896961049844, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755896961049938, "dur": 167, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1755896961050106, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1755896961050288, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1755896961050350, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1418726328684876121.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1755896961050476, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1418726328684876121.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1755896961050602, "dur": 159, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6413563044966999158.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1755896961050805, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755896961050864, "dur": 1313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755896961052178, "dur": 900, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755896961053078, "dur": 855, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755896961053934, "dur": 873, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755896961054808, "dur": 1242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755896961056051, "dur": 850, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755896961056901, "dur": 876, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755896961057777, "dur": 879, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755896961058657, "dur": 853, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755896961059511, "dur": 870, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755896961060382, "dur": 889, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755896961061272, "dur": 859, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755896961062132, "dur": 860, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755896961062993, "dur": 898, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755896961063891, "dur": 904, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755896961064799, "dur": 1343, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755896961066142, "dur": 846, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755896961066996, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1755896961067242, "dur": 274, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755896961067524, "dur": 1906, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1755896961069431, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755896961069568, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.Entities.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1755896961069775, "dur": 497, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.Entities.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1755896961070273, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755896961070352, "dur": 1158, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755896961071510, "dur": 2226, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755896961073737, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1755896961073890, "dur": 1217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1755896961075108, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755896961075252, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1755896961075361, "dur": 500, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1755896961075862, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755896961076052, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1755896961076184, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1755896961076431, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755896961076498, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1755896961076757, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755896961076823, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1755896961077072, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755896961077137, "dur": 408857, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755896961486008, "dur": 4307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 22, "ts": 1755896961490316, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755896961490443, "dur": 7158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 22, "ts": 1755896961497602, "dur": 257, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755896961497922, "dur": 1705, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1755896961499631, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755896961023963, "dur": 23750, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755896961047724, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_080CB5314CBF4724.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1755896961047823, "dur": 200, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_080CB5314CBF4724.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1755896961048027, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_DA76F58CBF310F62.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1755896961048124, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755896961048186, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_6941CE050E1CF5A2.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1755896961048239, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755896961048395, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_E38ED73995C25B9E.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1755896961048502, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_470099C400F771FA.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1755896961048689, "dur": 495, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_470099C400F771FA.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1755896961049187, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_DF909E95894C5387.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1755896961049283, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755896961049388, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1755896961049626, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755896961049768, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1755896961049901, "dur": 156, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp2"}}, {"pid": 12345, "tid": 23, "ts": 1755896961050315, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10397561839769426034.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1755896961050501, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5916180605834378387.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1755896961050599, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755896961050654, "dur": 267, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2400031028012695012.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1755896961050922, "dur": 1329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755896961052251, "dur": 919, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755896961053171, "dur": 1147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755896961054318, "dur": 933, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755896961055252, "dur": 1259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755896961056511, "dur": 1160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755896961057671, "dur": 991, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755896961058663, "dur": 914, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755896961059578, "dur": 969, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755896961060548, "dur": 1158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755896961061707, "dur": 850, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755896961062557, "dur": 894, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755896961063451, "dur": 91, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755896961063542, "dur": 933, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755896961064475, "dur": 255, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755896961064735, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755896961064802, "dur": 1322, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755896961066124, "dur": 887, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755896961067018, "dur": 285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1755896961067359, "dur": 1065, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1755896961068425, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755896961068685, "dur": 187, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755896961068873, "dur": 887, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755896961069760, "dur": 565, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755896961070327, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1755896961070506, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755896961070711, "dur": 442, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1755896961071153, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755896961071362, "dur": 812, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 23, "ts": 1755896961072175, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755896961072248, "dur": 541, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755896961073318, "dur": 405844, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 23, "ts": 1755896961485980, "dur": 4577, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 23, "ts": 1755896961490559, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755896961490663, "dur": 5125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 23, "ts": 1755896961495789, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755896961495982, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755896961496188, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755896961496435, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755896961496610, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755896961496719, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755896961496811, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755896961496970, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755896961497049, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755896961497178, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755896961497342, "dur": 2087, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1755896961499429, "dur": 490, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755896961023990, "dur": 23739, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755896961047730, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_5BF450534C33B93C.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1755896961047875, "dur": 161, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_5BF450534C33B93C.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1755896961048039, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_EFBDB924F7F1B25C.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1755896961048132, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755896961048194, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InsightsModule.dll_6EEC5CB3D74D8DC0.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1755896961048346, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755896961048409, "dur": 140, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_BA4F652EDE8ADBA9.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1755896961048551, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_B40F3C4651EACB98.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1755896961048730, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755896961048783, "dur": 227, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_B40F3C4651EACB98.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1755896961049014, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1755896961049075, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755896961049334, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1755896961049482, "dur": 256, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755896961049738, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 24, "ts": 1755896961049821, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755896961049962, "dur": 117, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp2"}}, {"pid": 12345, "tid": 24, "ts": 1755896961050080, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1755896961050374, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5546506141355401238.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1755896961050475, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12248871281822510316.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1755896961050575, "dur": 382, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12248871281822510316.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1755896961050958, "dur": 1278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755896961052236, "dur": 889, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755896961053125, "dur": 896, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755896961054021, "dur": 913, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755896961054935, "dur": 1249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755896961056720, "dur": 578, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.State\\TriggerStateTransition.cs"}}, {"pid": 12345, "tid": 24, "ts": 1755896961056185, "dur": 1389, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755896961057574, "dur": 844, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755896961058418, "dur": 867, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755896961059285, "dur": 856, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755896961060142, "dur": 913, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755896961061056, "dur": 831, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755896961061888, "dur": 860, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755896961062749, "dur": 950, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755896961063700, "dur": 943, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755896961064644, "dur": 177, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755896961064821, "dur": 1285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755896961066106, "dur": 926, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755896961067043, "dur": 347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1755896961067391, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755896961067452, "dur": 844, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1755896961068296, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755896961068488, "dur": 151, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755896961068639, "dur": 198, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755896961068838, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1755896961069125, "dur": 651, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1755896961069777, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755896961070002, "dur": 367, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755896961070369, "dur": 1035, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755896961071405, "dur": 212, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755896961071617, "dur": 4434, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755896961076052, "dur": 409897, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755896961485951, "dur": 4570, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 24, "ts": 1755896961490522, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755896961490623, "dur": 4974, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.Entities.dll (+pdb)"}}, {"pid": 12345, "tid": 24, "ts": 1755896961495598, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755896961495729, "dur": 3460, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 24, "ts": 1755896961499189, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1755896961499330, "dur": 517, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1755896961506977, "dur": 2147, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 19816, "tid": 12, "ts": 1755896961526165, "dur": 1820, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 19816, "tid": 12, "ts": 1755896961528035, "dur": 1820, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 19816, "tid": 12, "ts": 1755896961521303, "dur": 9339, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}